[project]
name = "openai-agents"
version = "0.2.11"
description = "OpenAI Agents SDK"
readme = "README.md"
requires-python = ">=3.9"
license = "MIT"
authors = [{ name = "OpenAI", email = "<EMAIL>" }]
dependencies = [
    "openai>=1.104.1,<2",
    "pydantic>=2.10, <3",
    "griffe>=1.5.6, <2",
    "typing-extensions>=4.12.2, <5",
    "requests>=2.0, <3",
    "types-requests>=2.0, <3",
    "mcp>=1.11.0, <2; python_version >= '3.10'",
]
classifiers = [
    "Typing :: Typed",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "License :: OSI Approved :: MIT License",
]

[project.urls]
Homepage = "https://openai.github.io/openai-agents-python/"
Repository = "https://github.com/openai/openai-agents-python"

[project.optional-dependencies]
voice = ["numpy>=2.2.0, <3; python_version>='3.10'", "websockets>=15.0, <16"]
viz = ["graphviz>=0.17"]
litellm = ["litellm>=1.67.4.post1, <2"]
realtime = ["websockets>=15.0, <16"]
sqlalchemy = ["SQLAlchemy>=2.0", "asyncpg>=0.29.0"]

[dependency-groups]
dev = [
    "mypy",
    "ruff==0.9.2",
    "pytest",
    "pytest-asyncio",
    "pytest-mock>=3.14.0",
    "rich>=13.1.0, <14",
    "mkdocs>=1.6.0",
    "mkdocs-material>=9.6.0",
    "mkdocstrings[python]>=0.28.0",
    "mkdocs-static-i18n",
    "coverage>=7.6.12",
    "playwright==1.50.0",
    "inline-snapshot>=0.20.7",
    "pynput",
    "types-pynput",
    "sounddevice",
    "textual",
    "websockets",
    "graphviz",
    "mkdocs-static-i18n>=1.3.0",
    "eval-type-backport>=0.2.2",
    "fastapi >= 0.110.0, <1",
    "aiosqlite>=0.21.0",
]

[tool.uv.workspace]
members = ["agents"]

[tool.uv.sources]
agents = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/agents"]


[tool.ruff]
line-length = 100
target-version = "py39"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
isort = { combine-as-imports = true, known-first-party = ["agents"] }

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.per-file-ignores]
"examples/**/*.py" = ["E501"]

[tool.mypy]
strict = true
disallow_incomplete_defs = false
disallow_untyped_defs = false
disallow_untyped_calls = false

[[tool.mypy.overrides]]
module = "sounddevice.*"
ignore_missing_imports = true

[tool.coverage.run]
source = ["tests", "src/agents"]

[tool.coverage.report]
show_missing = true
sort = "-Cover"
exclude_also = [
    # This is only executed while typechecking
    "if TYPE_CHECKING:",
    "@abc.abstractmethod",
    "raise NotImplementedError",
    "logger.debug",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "session"
filterwarnings = [
    # This is a warning that is expected to happen: we have an async filter that raises an exception
    "ignore:coroutine 'test_async_input_filter_fails.<locals>.invalid_input_filter' was never awaited:RuntimeWarning",
]
markers = [
    "allow_call_model_methods: mark test as allowing calls to real model implementations",
]

[tool.inline-snapshot]
format-command = "ruff format --stdin-filename {filename}"
