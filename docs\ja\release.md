---
search:
  exclude: true
---
# リリースプロセス/変更履歴

このプロジェクトは、`0.Y.Z` 形式のセマンティック バージョニングを一部変更した方法に従います。先頭の `0` は、SDK がまだ急速に進化していることを示します。各コンポーネントの増分ルールは次のとおりです。

## マイナー (`Y`) バージョン

ベータではない公開インターフェースに **breaking changes**（互換性のない変更） がある場合に、マイナー バージョン `Y` を増やします。たとえば、`0.0.x` から `0.1.x` への変更には互換性のない変更が含まれる可能性があります。

互換性のない変更を望まない場合は、プロジェクトで `0.0.x` バージョンにピン留めすることをおすすめします。

## パッチ (`Z`) バージョン

互換性を壊さない変更では `Z` を増やします:

- バグ修正
- 新機能
- 非公開インターフェースの変更
- ベータ 機能の更新

## 互換性のない変更の履歴

### 0.2.0

このバージョンでは、これまで引数として `Agent` を受け取っていたいくつかの箇所が、代わりに `AgentBase` を受け取るようになりました。たとえば、MCP サーバーにおける `list_tools()` 呼び出しなどです。これは純粋に型付け上の変更であり、引き続き `Agent` オブジェクトは受け取ります。更新するには、`Agent` を `AgentBase` に置き換えて型エラーを修正してください。

### 0.1.0

このバージョンでは、[`MCPServer.list_tools()`][agents.mcp.server.MCPServer] に `run_context` と `agent` の 2 つの新しいパラメーターが追加されました。`MCPServer` を継承するすべてのクラスに、これらのパラメーターを追加する必要があります。