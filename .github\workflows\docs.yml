name: Deploy docs

on:
  workflow_run:
    workflows: ["Tests"]
    types:
      - completed

permissions:
  contents: write # This allows pushing to gh-pages

jobs:
  deploy_docs:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: make sync
      - name: Deploy docs
        run: make deploy-docs
