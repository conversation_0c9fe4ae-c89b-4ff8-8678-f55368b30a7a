---
search:
  exclude: true
---
# コード例

[リポジトリ](https://github.com/openai/openai-agents-python/tree/main/examples) の code examples セクションで、SDK のさまざまなサンプル実装をご覧ください。これらのコード例は、さまざまなパターンと機能を示すいくつかのカテゴリーに整理されています。


## カテゴリー

- **[agent_patterns](https://github.com/openai/openai-agents-python/tree/main/examples/agent_patterns):**
  このカテゴリーのコード例は、一般的なエージェント設計パターンを示します

    - 決定的なワークフロー
    - ツールとしてのエージェント
    - エージェントの並列実行

- **[basic](https://github.com/openai/openai-agents-python/tree/main/examples/basic):**
  これらのコード例は、SDK の基礎的な機能を紹介します

    - 動的な システムプロンプト
    - ストリーミング出力
    - ライフサイクルイベント

- **[ツールのコード例](https://github.com/openai/openai-agents-python/tree/main/examples/tools):**
  Web 検索 や ファイル検索 などの OpenAI がホストするツールの実装方法と、
   それらをエージェントに統合する方法を学べます。

- **[model providers](https://github.com/openai/openai-agents-python/tree/main/examples/model_providers):**
  SDK で OpenAI 以外のモデルを使用する方法を確認してください。

- **[handoffs](https://github.com/openai/openai-agents-python/tree/main/examples/handoffs):**
  エージェントのハンドオフの実用的な例をご覧ください。

- **[mcp](https://github.com/openai/openai-agents-python/tree/main/examples/mcp):**
  MCP でエージェントを構築する方法を学べます。

- **[customer_service](https://github.com/openai/openai-agents-python/tree/main/examples/customer_service)** と **[research_bot](https://github.com/openai/openai-agents-python/tree/main/examples/research_bot):**
  実運用に近いアプリケーションを示す、さらに作り込まれた 2 つの例

    - **customer_service**: 航空会社向けのカスタマーサービスシステムの例。
    - **research_bot**: シンプルな ディープリサーチ のクローン。

- **[voice](https://github.com/openai/openai-agents-python/tree/main/examples/voice):**
  TTS と STT のモデルを使った音声エージェントの例。

- **[realtime](https://github.com/openai/openai-agents-python/tree/main/examples/realtime):**
  SDK を使ってリアルタイムなエクスペリエンスを構築する方法を示すコード例。