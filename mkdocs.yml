site_name: OpenAI Agents SDK
theme:
  name: material
  features:
    # Allows copying code blocks
    - content.code.copy
    # Allows selecting code blocks
    - content.code.select
    # Shows the current path in the sidebar
    - navigation.path
    # Shows sections in the sidebar
    - navigation.sections
    # Shows sections expanded by default
    - navigation.expand
    # Enables annotations in code blocks
    - content.code.annotate
  palette:
    primary: black
  logo: assets/logo.svg
  favicon: images/favicon-platform.svg

repo_name: openai-agents-python
repo_url: https://github.com/openai/openai-agents-python

plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          paths: ["src/agents"]
          selection:
            docstring_style: google
          options:
            # Shows links to other members in signatures
            signature_crossrefs: true
            # Orders members by source order, rather than alphabetical
            members_order: source
            # Puts the signature on a separate line from the member name
            separate_signature: true
            # Shows type annotations in signatures
            show_signature_annotations: true
            # Makes the font sizes nicer
            heading_level: 3
            # Show inherited members
            inherited_members: true
  - i18n:
      docs_structure: folder
      languages:
        - locale: en
          default: true
          name: English
          build: true
          nav:
            - Intro: index.md
            - Quickstart: quickstart.md
            - Examples: examples.md
            - Documentation:
                - agents.md
                - running_agents.md
                - sessions.md
                - results.md
                - streaming.md
                - repl.md
                - tools.md
                - mcp.md
                - handoffs.md
                - tracing.md
                - context.md
                - guardrails.md
                - multi_agent.md
                - usage.md
                - Models:
                    - models/index.md
                    - models/litellm.md
                - config.md
                - visualization.md
                - release.md
                - Voice agents:
                    - voice/quickstart.md
                    - voice/pipeline.md
                    - voice/tracing.md
                - Realtime agents:
                    - realtime/quickstart.md
                    - realtime/guide.md
            - API Reference:
                - Agents:
                    - ref/index.md
                    - ref/agent.md
                    - ref/run.md
                    - ref/memory.md
                    - ref/repl.md
                    - ref/tool.md
                    - ref/tool_context.md
                    - ref/result.md
                    - ref/stream_events.md
                    - ref/handoffs.md
                    - ref/lifecycle.md
                    - ref/items.md
                    - ref/run_context.md
                    - ref/tool_context.md
                    - ref/usage.md
                    - ref/exceptions.md
                    - ref/guardrail.md
                    - ref/model_settings.md
                    - ref/agent_output.md
                    - ref/function_schema.md
                    - ref/models/interface.md
                    - ref/models/openai_chatcompletions.md
                    - ref/models/openai_responses.md
                    - ref/mcp/server.md
                    - ref/mcp/util.md
                - Tracing:
                    - ref/tracing/index.md
                    - ref/tracing/create.md
                    - ref/tracing/traces.md
                    - ref/tracing/spans.md
                    - ref/tracing/processor_interface.md
                    - ref/tracing/processors.md
                    - ref/tracing/scope.md
                    - ref/tracing/setup.md
                    - ref/tracing/span_data.md
                    - ref/tracing/util.md
                - Realtime:
                    - ref/realtime/agent.md
                    - ref/realtime/runner.md
                    - ref/realtime/session.md
                    - ref/realtime/events.md
                    - ref/realtime/config.md
                    - ref/realtime/model.md
                - Voice:
                    - ref/voice/pipeline.md
                    - ref/voice/workflow.md
                    - ref/voice/input.md
                    - ref/voice/result.md
                    - ref/voice/pipeline_config.md
                    - ref/voice/events.md
                    - ref/voice/exceptions.md
                    - ref/voice/model.md
                    - ref/voice/utils.md
                    - ref/voice/models/openai_provider.md
                    - ref/voice/models/openai_stt.md
                    - ref/voice/models/openai_tts.md
                - Extensions:
                    - ref/extensions/handoff_filters.md
                    - ref/extensions/handoff_prompt.md
                    - ref/extensions/litellm.md
                    - ref/extensions/memory/sqlalchemy_session.md

        - locale: ja
          name: 日本語
          build: true
          nav:
            - はじめに: index.md
            - クイックスタート: quickstart.md
            - コード例: examples.md
            - ドキュメント:
                - agents.md
                - running_agents.md
                - sessions.md
                - results.md
                - streaming.md
                - repl.md
                - tools.md
                - mcp.md
                - handoffs.md
                - tracing.md
                - context.md
                - guardrails.md
                - multi_agent.md
                - usage.md
                - モデル:
                    - models/index.md
                    - models/litellm.md
                - config.md
                - visualization.md
                - 音声エージェント:
                    - voice/quickstart.md
                    - voice/pipeline.md
                    - voice/tracing.md
                - リアルタイムエージェント:
                    - realtime/quickstart.md
                    - realtime/guide.md

extra:
  # Remove material generation message in footer
  generator: false
  language: en
  alternate:
    - name: English
      link: /openai-agents-python/
      lang: en
    - name: 日本語
      link: /openai-agents-python/ja/
      lang: ja

markdown_extensions:
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - admonition
  - pymdownx.details
  - attr_list
  - md_in_html
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

validation:
  omitted_files: warn
  absolute_links: warn
  unrecognized_links: warn
  anchors: warn

extra_css:
  - stylesheets/extra.css

watch:
  - "src/agents"
