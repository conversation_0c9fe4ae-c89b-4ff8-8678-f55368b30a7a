---
search:
  exclude: true
---
# 複数のエージェントのオーケストレーション

オーケストレーションとは、アプリ内でのエージェントの流れを指します。どのエージェントを、どの順序で実行し、次に何をするかをどのように決めるのか。エージェントをオーケストレーションする主な方法は 2 つあります。

1. LLM に判断を任せる: これは、 LLM の知性を活用して計画・推論し、それに基づいて次に取るべきステップを決定します。
2. コードによるオーケストレーション: コードでエージェントのフローを決めます。

これらのパターンは組み合わせて使えます。それぞれにトレードオフがあります（以下参照）。

## LLM によるオーケストレーション

エージェントは、 instructions、tools、ハンドオフ を備えた LLM です。つまり、オープンエンドなタスクが与えられたとき、 LLM は自律的にタスクへの取り組み方を計画し、ツールを使ってアクションを実行・データを取得し、ハンドオフ を使ってサブエージェントへタスクを委任できます。例えば、リサーチ用のエージェントには次のようなツールを備えられます。

- Web 検索でオンラインの情報を見つける
- ファイル検索 と取得でプロプライエタリなデータやコネクションを横断検索する
- コンピュータ操作 でコンピュータ上のアクションを実行する
- コード実行 でデータ分析を行う
- 計画立案、レポート作成などが得意な専門エージェントへの ハンドオフ

このパターンは、タスクがオープンエンドで、 LLM の知性に任せたい場合に最適です。ここで重要な戦術は次のとおりです。

1. 良いプロンプトに投資する。利用可能なツール、その使い方、準拠すべきパラメーターを明確にします。
2. アプリをモニタリングし、反復改善する。問題が起きる箇所を観察し、プロンプトを改善します。
3. エージェントに内省と改善を許可する。例えばループで実行して自己批判させる、あるいはエラーメッセージを与えて改善させます。
4. 何でもこなす汎用エージェントではなく、1 つのタスクに特化して優れたエージェントを用意する。
5. [Evals](https://platform.openai.com/docs/guides/evals) に投資する。これによりエージェントを訓練し、タスク遂行能力を向上できます。

## コードによるオーケストレーション

LLM によるオーケストレーションは強力ですが、コードによるオーケストレーションは速度・コスト・パフォーマンスの観点で、より決定的かつ予測可能になります。一般的なパターンは次のとおりです。

- [structured outputs](https://platform.openai.com/docs/guides/structured-outputs) を使って、コードで検査できる 適切な形式のデータ を生成する。例えば、エージェントにタスクをいくつかの カテゴリー に分類させ、そのカテゴリー に基づいて次のエージェントを選ぶなど。
- あるエージェントの出力を次のエージェントの入力へと変換して、複数のエージェントを連鎖させる。ブログ記事の執筆を、リサーチ → アウトライン作成 → 本文執筆 → 批評 → 改善といった一連のステップに分解できます。
- 評価してフィードバックを与えるエージェントと、タスクを実行するエージェントを `while` ループで回し、評価者が出力が一定基準を満たしたと判断するまで実行する。
- 複数のエージェントを並列実行する（例: Python の基本コンポーネントである `asyncio.gather` を使う）。相互依存しないタスクが複数あるとき、速度向上に有用です。

[`examples/agent_patterns`](https://github.com/openai/openai-agents-python/tree/main/examples/agent_patterns) に多数のコード例があります。